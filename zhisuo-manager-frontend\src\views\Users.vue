<template>
  <div class="users-page">
    <!-- 统一的管理面板 -->
    <div class="management-panel">
      <!-- 页面标题和统计 -->
      <div class="panel-header">
        <div class="header-left">
          <h2>用户管理</h2>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item">
              <span class="stat-label">总用户数</span>
              <span class="stat-value">{{ totalUsers }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">今日新增</span>
              <span class="stat-value">{{ todayNewUsers }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">活跃用户</span>
              <span class="stat-value">{{ recentActiveUsers }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">禁用用户</span>
              <span class="stat-value">{{ disabledUsers }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="panel-divider"></div>

      <!-- 搜索和筛选区域 -->
      <div class="panel-filters">
        <!-- 第一行：筛选条件 + 搜索重置按钮 -->
        <div class="filter-row">
          <div class="filter-content">
            <el-form :model="queryForm" inline class="filter-form" label-width="60px">
              <el-form-item label="手机号">
                <el-input
                  v-model="queryForm.phone"
                  placeholder="请输入手机号"
                  clearable
                  style="width: 120px"
                />
              </el-form-item>

              <el-form-item label="昵称">
                <el-input
                  v-model="queryForm.nickname"
                  placeholder="请输入昵称"
                  clearable
                  style="width: 120px"
                />
              </el-form-item>

              <el-form-item label="会员等级">
                <el-select v-model="queryForm.memberLevel" placeholder="请选择" clearable style="width: 110px">
                  <el-option label="普通会员" :value="0" />
                  <el-option label="黄金会员" :value="1" />
                  <el-option label="铂金会员" :value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="状态">
                <el-select v-model="queryForm.status" placeholder="请选择" clearable style="width: 90px">
                  <el-option label="正常" :value="1" />
                  <el-option label="禁用" :value="0" />
                </el-select>
              </el-form-item>

              <el-form-item label="注册时间">
                <el-date-picker
                  v-model="queryForm.registerTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                  style="width: 240px"
                />
              </el-form-item>
            </el-form>
          </div>

          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </div>

        <!-- 第二行：管理按钮 -->
        <div class="action-row">
          <div class="action-buttons">
            <el-button type="primary" @click="handleAddUser">
              <el-icon><Plus /></el-icon>
              添加用户
            </el-button>
            <el-button
              type="danger"
              :disabled="selectedUsers.length === 0"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedUsers.length }})
            </el-button>
            <el-button @click="handleExport">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 用户列表 -->
    <div ref="userListRef" class="content-card">
      <div class="table-header">
        <div class="table-title">用户列表</div>
        <div class="table-actions">
          <el-button size="small" @click="handleRefresh" class="refresh-btn">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
      
      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <!-- 勾选框列 -->
        <el-table-column type="selection" width="55" />

        <!-- 序号列 -->
        <el-table-column label="序号" width="80" type="index" :index="getRowIndex" />
        
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip />
        
        <el-table-column label="会员等级" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.memberLevel === 0 ? 'info' : row.memberLevel === 1 ? 'warning' : 'success'"
              size="small"
            >
              {{ row.memberLevelName }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="favoriteCount" label="收藏数" width="80" />
        <el-table-column prop="likeCount" label="点赞数" width="80" />
        <el-table-column prop="commentCount" label="评论数" width="80" />
        
        <el-table-column prop="createTime" label="注册时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="lastLoginTime" label="最后登录" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastLoginTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="260" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEditUser(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteUser(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 添加用户对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      width="500px"
      :before-close="handleCloseAdd"
      title="添加用户"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="100px"
        class="add-user-form"
      >
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="addForm.phone"
            placeholder="请输入手机号"
            maxlength="11"
            clearable
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="addForm.password"
            type="password"
            placeholder="请输入密码"
            maxlength="20"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model="addForm.nickname"
            placeholder="留空则自动生成：用户+手机号后四位"
            maxlength="50"
            clearable
          />
        </el-form-item>

        <el-form-item label="会员等级" prop="memberLevel">
          <el-select v-model="addForm.memberLevel" placeholder="请选择会员等级" style="width: 100%">
            <el-option label="普通会员" :value="0" />
            <el-option label="黄金会员" :value="1" />
            <el-option label="铂金会员" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="addForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseAdd">取消</el-button>
          <el-button type="primary" @click="handleSubmitAdd" :loading="addLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      width="500px"
      :before-close="handleCloseEdit"
      title="编辑用户"
    >
      <div v-if="editForm.userId" class="edit-user-header">
        <div class="user-avatar-section">
          <el-avatar
            :size="60"
            :src="editForm.avatar"
            :alt="editForm.nickname || '用户头像'"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="user-basic-info">
            <h4>{{ editForm.nickname || '用户' + editForm.userId }}</h4>
            <p class="user-id">ID: {{ editForm.userId }}</p>
          </div>
        </div>
      </div>

      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="100px"
        class="edit-user-form"
      >
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="editForm.phone"
            placeholder="请输入手机号"
            maxlength="11"
            clearable
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="editForm.password"
            type="password"
            placeholder="留空则不修改密码"
            maxlength="20"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model="editForm.nickname"
            placeholder="请输入昵称"
            maxlength="50"
            clearable
          />
        </el-form-item>

        <el-form-item label="会员等级" prop="memberLevel">
          <el-select v-model="editForm.memberLevel" placeholder="请选择会员等级" style="width: 100%">
            <el-option label="普通会员" :value="0" />
            <el-option label="黄金会员" :value="1" />
            <el-option label="铂金会员" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseEdit">取消</el-button>
          <el-button type="primary" @click="handleSubmitEdit" :loading="editLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      width="650px"
      :before-close="handleCloseDetail"
      class="user-detail-dialog"
      title="用户详情"
    >
      <div v-if="currentUser" class="user-detail-content">
        <!-- 用户头像和基本信息 -->
        <div class="user-header">
          <div class="user-avatar">
            <el-avatar
              :size="60"
              :src="currentUser.avatar"
              :alt="currentUser.nickname || '用户' + currentUser.userId"
            >
              <el-icon><User /></el-icon>
            </el-avatar>
          </div>
          <div class="user-info">
            <h3 class="user-name">{{ currentUser.nickname || '用户' + currentUser.userId }}</h3>
            <div class="user-tags">
              <el-tag
                :type="currentUser.memberLevel === 0 ? 'info' : currentUser.memberLevel === 1 ? 'warning' : 'success'"
                size="small"
              >
                {{ currentUser.memberLevelName }}
              </el-tag>
              <el-tag
                :type="currentUser.status === 1 ? 'success' : 'danger'"
                size="small"
                class="status-tag"
              >
                {{ currentUser.statusName }}
              </el-tag>
            </div>
          </div>
          <!-- 活动统计 -->
          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-number">{{ currentUser.favoriteCount }}</div>
              <div class="stat-label">收藏</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ currentUser.likeCount }}</div>
              <div class="stat-label">点赞</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ currentUser.commentCount }}</div>
              <div class="stat-label">评论</div>
            </div>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="detail-section">
          <div class="detail-group">
            <h4 class="group-title">基本信息</h4>
            <div class="detail-items">
              <div class="detail-item">
                <span class="item-label">用户ID</span>
                <div class="item-value-with-copy">
                  <el-tooltip :content="currentUser.userId" placement="top" class="tooltip-wrapper">
                    <span class="item-value truncated">{{ currentUser.userId }}</span>
                  </el-tooltip>
                  <el-button
                    type="text"
                    size="small"
                    class="copy-btn"
                    @click="copyToClipboard(currentUser.userId)"
                  >
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="detail-item">
                <span class="item-label">手机号</span>
                <div class="item-value-with-copy">
                  <span class="item-value">{{ currentUser.phone }}</span>
                  <el-button
                    type="text"
                    size="small"
                    class="copy-btn"
                    @click="copyToClipboard(currentUser.phone)"
                  >
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                </div>
              </div>

            </div>
          </div>

          <div class="detail-group">
            <h4 class="group-title">时间信息</h4>
            <div class="detail-items">
              <div class="detail-item">
                <span class="item-label">注册时间</span>
                <span class="item-value">{{ formatDateTime(currentUser.createTime) }}</span>
              </div>
              <div class="detail-item">
                <span class="item-label">最后登录</span>
                <span class="item-value">{{ formatDateTime(currentUser.lastLoginTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  Search,
  Refresh,
  User,
  CopyDocument,
  Delete
} from '@element-plus/icons-vue'
import { userApi } from '@/api/user'
import { formatDateTime } from '@/utils/dateFormat'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const detailDialogVisible = ref(false)
const currentUser = ref(null)
const userListRef = ref(null)
const selectedUsers = ref([]) // 选中的用户列表

// 添加用户相关
const addDialogVisible = ref(false)
const addLoading = ref(false)
const addFormRef = ref(null)

// 编辑用户相关
const editDialogVisible = ref(false)
const editLoading = ref(false)
const editFormRef = ref(null)

// 统计数据
const totalUsers = ref(0)
const todayNewUsers = ref(0)
const activeUsers = ref(0)
const disabledUsers = ref(0)
const recentActiveUsers = ref(0)

// 查询表单
const queryForm = reactive({
  phone: '',
  nickname: '',
  memberLevel: null,
  status: null,
  registerTimeRange: null
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 添加用户表单
const addForm = reactive({
  phone: '',
  password: '',
  nickname: '',
  memberLevel: 0,
  status: 1
})

// 添加用户表单验证规则
const addFormRules = reactive({
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度必须在6-20位之间', trigger: 'blur' }
  ],
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  memberLevel: [
    { required: true, message: '请选择会员等级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 编辑用户表单
const editForm = reactive({
  userId: '',
  phone: '',
  password: '',
  nickname: '',
  avatar: '',
  memberLevel: 0,
  status: 1
})

// 编辑用户表单验证规则
const editFormRules = reactive({
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  password: [
    { min: 6, max: 20, message: '密码长度必须在6-20位之间', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  memberLevel: [
    { required: true, message: '请选择会员等级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// formatDateTime 函数已从 @/utils/dateFormat 导入

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('复制成功')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchUserList()
}

// 重置
const handleReset = () => {
  Object.keys(queryForm).forEach(key => {
    if (key === 'status' || key === 'memberLevel' || key === 'registerTimeRange') {
      queryForm[key] = null
    } else {
      queryForm[key] = ''
    }
  })
  pagination.current = 1
  fetchUserList()
  // 滚动到用户列表
  scrollToUserList()
}

// 刷新
const handleRefresh = () => {
  fetchUserList()
  // 滚动到用户列表
  scrollToUserList()
}

// 滚动到用户列表
const scrollToUserList = () => {
  setTimeout(() => {
    if (userListRef.value) {
      userListRef.value.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }, 100)
}

// 获取行索引（考虑分页）
const getRowIndex = (index) => {
  return (pagination.current - 1) * pagination.size + index + 1
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}



// 查看详情
const handleViewDetail = async (user) => {
  try {
    const response = await userApi.getUserDetail(user.userId)
    if (response.code === 200) {
      currentUser.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

// 关闭详情对话框
const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentUser.value = null
}

// 编辑用户
const handleEditUser = async (user) => {
  try {
    // 获取用户详情
    const response = await userApi.getUserDetail(user.userId)
    if (response.code === 200) {
      const userDetail = response.data
      // 填充编辑表单
      Object.assign(editForm, {
        userId: userDetail.userId,
        phone: userDetail.phone,
        password: '', // 密码留空
        nickname: userDetail.nickname,
        avatar: userDetail.avatar,
        memberLevel: userDetail.memberLevel,
        status: userDetail.status
      })
      editDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

// 关闭编辑用户对话框
const handleCloseEdit = () => {
  editDialogVisible.value = false
  resetEditForm()
}

// 重置编辑用户表单
const resetEditForm = () => {
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
  Object.assign(editForm, {
    userId: '',
    phone: '',
    password: '',
    nickname: '',
    avatar: '',
    memberLevel: 0,
    status: 1
  })
}

// 提交编辑用户
const handleSubmitEdit = async () => {
  if (!editFormRef.value) return

  try {
    // 表单验证
    await editFormRef.value.validate()

    editLoading.value = true

    // 构建更新数据
    const updateData = {
      userId: editForm.userId,
      phone: editForm.phone,
      nickname: editForm.nickname,
      memberLevel: editForm.memberLevel,
      status: editForm.status
    }

    // 如果密码不为空，则包含密码
    if (editForm.password && editForm.password.trim()) {
      updateData.password = editForm.password
    }

    const response = await userApi.updateUser(updateData)

    if (response.code === 200) {
      ElMessage.success('更新用户成功')
      editDialogVisible.value = false
      resetEditForm()

      // 刷新用户列表和统计数据
      await fetchUserList()
      await fetchUserStatistics()
    } else {
      ElMessage.error(response.message || '更新用户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新用户失败:', error)
      ElMessage.error('更新用户失败')
    }
  } finally {
    editLoading.value = false
  }
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchUserList()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchUserList()
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    // 处理查询参数
    const params = {
      current: pagination.current,
      size: pagination.size,
      phone: queryForm.phone,
      nickname: queryForm.nickname,
      memberLevel: queryForm.memberLevel,
      status: queryForm.status
    }

    // 处理注册时间范围
    if (queryForm.registerTimeRange && queryForm.registerTimeRange.length === 2) {
      params.startTime = queryForm.registerTimeRange[0] + ' 00:00:00'
      params.endTime = queryForm.registerTimeRange[1] + ' 23:59:59'
    }

    const response = await userApi.getUserList(params)
    console.log('API响应数据:', response)

    if (response.code === 200) {
      userList.value = response.data.records || []
      pagination.total = response.data.total || 0
      console.log('用户列表数据:', userList.value.length, '条')
      console.log('分页总数:', pagination.total)
      console.log('完整响应数据:', response.data)
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')

    // 清空数据
    userList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 添加用户
const handleAddUser = () => {
  addDialogVisible.value = true
}

// 关闭添加用户对话框
const handleCloseAdd = () => {
  addDialogVisible.value = false
  resetAddForm()
}

// 重置添加用户表单
const resetAddForm = () => {
  if (addFormRef.value) {
    addFormRef.value.resetFields()
  }
  Object.assign(addForm, {
    phone: '',
    password: '',
    nickname: '',
    memberLevel: 0,
    status: 1
  })
}

// 提交添加用户
const handleSubmitAdd = async () => {
  if (!addFormRef.value) return

  try {
    // 表单验证
    await addFormRef.value.validate()

    addLoading.value = true

    const response = await userApi.createUser(addForm)

    if (response.code === 200) {
      ElMessage.success('添加用户成功')
      addDialogVisible.value = false
      resetAddForm()

      // 刷新用户列表和统计数据
      await fetchUserList()
      await fetchUserStatistics()
    } else {
      ElMessage.error(response.message || '添加用户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('添加用户失败:', error)
      ElMessage.error('添加用户失败')
    }
  } finally {
    addLoading.value = false
  }
}

// 导出数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 删除单个用户
const handleDeleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要永久删除用户 "${user.nickname || user.phone}" 吗？此操作将从数据库中彻底删除该用户的所有数据，无法恢复！`,
      '永久删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const response = await userApi.deleteUser(user.userId)

    if (response.code === 200) {
      ElMessage.success('用户已永久删除')
      // 刷新用户列表和统计数据
      await fetchUserList()
      await fetchUserStatistics()
    } else {
      ElMessage.error(response.message || '删除用户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

// 批量删除用户
const handleBatchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要删除的用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要永久删除选中的 ${selectedUsers.value.length} 个用户吗？此操作将从数据库中彻底删除这些用户的所有数据，无法恢复！`,
      '批量永久删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const userIds = selectedUsers.value.map(user => user.userId)
    const response = await userApi.batchDeleteUsers(userIds)

    if (response.code === 200) {
      ElMessage.success(`已永久删除 ${selectedUsers.value.length} 个用户`)
      // 清空选择
      selectedUsers.value = []
      // 刷新用户列表和统计数据
      await fetchUserList()
      await fetchUserStatistics()
    } else {
      ElMessage.error(response.message || '批量删除用户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除用户失败:', error)
      ElMessage.error('批量删除用户失败')
    }
  }
}

// 获取用户统计数据
const fetchUserStatistics = async () => {
  try {
    const response = await userApi.getUserStatistics()
    if (response.code === 200) {
      const stats = response.data
      totalUsers.value = stats.totalUsers || 0
      activeUsers.value = stats.activeUsers || 0
      disabledUsers.value = stats.disabledUsers || 0
      recentActiveUsers.value = stats.recentActiveUsers || 0

      // 今日新增用户数
      todayNewUsers.value = stats.todayNewUsers || 0

      console.log('获取统计数据成功:', stats)
    } else {
      console.error('获取统计数据失败:', response.message)
      // 清空统计数据
      totalUsers.value = 0
      todayNewUsers.value = 0
      activeUsers.value = 0
      disabledUsers.value = 0
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 清空统计数据
    totalUsers.value = 0
    todayNewUsers.value = 0
    activeUsers.value = 0
    disabledUsers.value = 0
  }
}



onMounted(() => {
  fetchUserList()
  fetchUserStatistics()
})
</script>

<style lang="scss" scoped>
.users-page {
  .management-panel {
    margin-bottom: 32px;
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    overflow: hidden;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 24px 0 24px;

      .header-left {
        h2 {
          font-size: 28px;
          font-weight: 700;
          color: #1F2937;
          margin: 0;
        }
      }

      .header-right {
        .header-stats {
          display: flex;
          gap: 24px;

          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 20px;
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
            border-radius: 12px;
            border: 1px solid #E2E8F0;
            min-width: 100px;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
              border-color: #8B5CF6;
            }

            .stat-label {
              font-size: 12px;
              color: #64748B;
              margin-bottom: 4px;
              font-weight: 500;
            }

            .stat-value {
              font-size: 24px;
              font-weight: 700;
              color: #8B5CF6;
            }
          }
        }
      }
    }

    .panel-divider {
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, #E5E7EB 20%, #E5E7EB 80%, transparent 100%);
      margin: 24px 0;
    }

    .panel-filters {
      padding: 0 24px 24px 24px;

      .filter-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .filter-content {
          flex: 1;

          .filter-form {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            gap: 16px;

            .el-form-item {
              margin-bottom: 0;
              margin-right: 0;
              flex-shrink: 0;

              // 调整标签宽度和居中显示
              :deep(.el-form-item__label) {
                width: 60px !important;
                text-align: center;
                padding-right: 6px;
                font-size: 13px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }

        .search-buttons {
          display: flex;
          gap: 12px;
          flex-shrink: 0;

          .el-button {
            height: 36px;
            padding: 0 16px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;

            &.el-button--primary {
              background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
              border: none;

              &:hover {
                background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
              }
            }

            &:not(.el-button--primary) {
              background: #F9FAFB;
              border: 1px solid #E5E7EB;
              color: #374151;

              &:hover {
                background: #F3F4F6;
                border-color: #D1D5DB;
                transform: translateY(-1px);
              }
            }
          }
        }
      }

      .action-row {
        display: flex;
        align-items: center;

        .action-buttons {
          display: flex;
          gap: 12px;

          .el-button {
            height: 36px;
            padding: 0 16px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;

            &.el-button--primary {
              background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
              border: none;

              &:hover {
                background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
              }
            }

            &.el-button--danger {
              background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
              border: none;
              color: white;

              &:hover:not(.is-disabled) {
                background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
              }

              &.is-disabled {
                background: #F3F4F6;
                color: #9CA3AF;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
              }
            }

            &:not(.el-button--primary):not(.el-button--danger) {
              background: #F9FAFB;
              border: 1px solid #E5E7EB;
              color: #374151;

              &:hover {
                background: #F3F4F6;
                border-color: #D1D5DB;
                transform: translateY(-1px);
              }
            }
          }
        }
      }
    }
  }
  
  .content-card {
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    overflow: hidden;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #F3F4F6;
      background: #FAFBFC;

      .table-title {
        font-size: 18px;
        font-weight: 600;
        color: #1F2937;
      }

      .table-actions {
        .refresh-btn {
          width: 32px;
          height: 32px;
          padding: 0;
          border-radius: 6px;
          background: #F9FAFB;
          border: 1px solid #E5E7EB;
          color: #6B7280;

          &:hover {
            background: #F3F4F6;
            border-color: #D1D5DB;
            color: #374151;
            transform: rotate(180deg);
            transition: all 0.3s ease;
          }

          .el-icon {
            font-size: 16px;
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .user-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }

  // 用户详情对话框样式
  :deep(.user-detail-dialog) {
    .el-dialog__body {
      padding: 20px;
    }

    .user-detail-content {
      // 用户头像和基本信息
      .user-header {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 20px;
        background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
        border-radius: 12px;
        margin-bottom: 20px;
        color: white;

        .user-avatar {
          flex-shrink: 0;

          .el-avatar {
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.2);

            .el-icon {
              font-size: 24px;
              color: white;
            }
          }
        }

        .user-info {
          flex: 1;

          .user-name {
            margin: 0 0 6px 0;
            font-size: 18px;
            font-weight: 600;
            color: white;
          }

          .user-phone {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }

          .user-tags {
            display: flex;
            gap: 8px;

            .el-tag {
              border: none;
              font-weight: 500;

              // 会员等级标签保持白色背景
              &.el-tag--info,
              &.el-tag--warning,
              &.el-tag--success:not(.status-tag) {
                background: rgba(255, 255, 255, 0.9);
                color: #374151;
              }

              // 状态标签使用对应的颜色
              &.el-tag--success.status-tag {
                background: rgba(34, 197, 94, 0.9);
                color: white;
              }

              &.el-tag--danger.status-tag {
                background: rgba(239, 68, 68, 0.9);
                color: white;
              }
            }
          }
        }

        .user-stats {
          display: flex;
          gap: 24px;

          .stat-item {
            text-align: center;
            min-width: 60px;

            .stat-number {
              font-size: 20px;
              font-weight: 700;
              color: white;
              margin-bottom: 4px;
            }

            .stat-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
      }

      // 详细信息
      .detail-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        align-items: stretch;

        .detail-group {
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
          display: flex;
          flex-direction: column;

          .group-title {
            margin: 0;
            padding: 12px 16px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          }

          .detail-items {
            padding: 16px;
            flex: 1;
            display: flex;
            flex-direction: column;

            .detail-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 0;
              border-bottom: 1px solid #f3f4f6;
              min-height: 44px;
              flex: 1;

              &:last-child {
                border-bottom: none;
              }

              .item-label {
                font-size: 13px;
                color: #6b7280;
                font-weight: 500;
                flex-shrink: 0;
                width: 80px;
                text-align: left;
              }

              .item-value {
                font-size: 13px;
                color: #111827;
                font-weight: 600;
                flex: 1;
                text-align: right;
              }

              .item-value-with-copy {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 8px;
                flex: 1;
                min-width: 0;

                .tooltip-wrapper {
                  display: inline-block;
                  flex-shrink: 1;
                  min-width: 0;
                }

                .item-value {
                  font-size: 13px;
                  color: #111827;
                  font-weight: 600;

                  &.truncated {
                    max-width: 150px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    cursor: help;
                    display: inline-block;
                  }
                }

                .copy-btn {
                  padding: 4px;
                  min-height: auto;
                  color: #6b7280;
                  flex-shrink: 0;

                  &:hover {
                    color: #667eea;
                    background-color: #f3f4f6;
                  }

                  .el-icon {
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 添加用户对话框样式
  :deep(.el-dialog) {
    .add-user-form {
      .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
          font-weight: 500;
          color: #374151;
        }

        .el-input__wrapper {
          border-radius: 6px;
        }

        .el-select {
          .el-input__wrapper {
            border-radius: 6px;
          }
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;

      .el-button {
        padding: 8px 20px;
        border-radius: 6px;
        font-weight: 500;

        &.el-button--primary {
          background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
          }
        }
      }
    }
  }

  // 编辑用户对话框样式
  .edit-user-header {
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;

    .user-avatar-section {
      display: flex;
      align-items: center;
      gap: 15px;

      .el-avatar {
        border: 2px solid rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.2);

        .el-icon {
          font-size: 24px;
          color: white;
        }
      }

      .user-basic-info {
        h4 {
          margin: 0 0 5px 0;
          font-size: 18px;
          font-weight: 600;
          color: white;
        }

        .user-id {
          margin: 0;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          font-family: monospace;
        }
      }
    }
  }
}
</style>
