/**
 * 日期格式化工具
 * 统一日期格式为：xxxx/xx/xx xx:xx:xx
 */

/**
 * 格式化日期时间为固定格式：xxxx/xx/xx xx:xx:xx
 * @param {Date|string|number} dateTime - 日期时间对象、字符串或时间戳
 * @returns {string} 格式化后的日期字符串，格式为 xxxx/xx/xx xx:xx:xx
 */
export function formatDateTime(dateTime) {
  if (!dateTime) return '-'
  
  const date = new Date(dateTime)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '-'
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 格式化日期为固定格式：xxxx/xx/xx
 * @param {Date|string|number} dateTime - 日期时间对象、字符串或时间戳
 * @returns {string} 格式化后的日期字符串，格式为 xxxx/xx/xx
 */
export function formatDate(dateTime) {
  if (!dateTime) return '-'
  
  const date = new Date(dateTime)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '-'
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return `${year}/${month}/${day}`
}

/**
 * 格式化时间为固定格式：xx:xx:xx
 * @param {Date|string|number} dateTime - 日期时间对象、字符串或时间戳
 * @returns {string} 格式化后的时间字符串，格式为 xx:xx:xx
 */
export function formatTime(dateTime) {
  if (!dateTime) return '-'
  
  const date = new Date(dateTime)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '-'
  
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${hours}:${minutes}:${seconds}`
}

/**
 * 格式化日期时间为不带秒的格式：xxxx/xx/xx xx:xx
 * @param {Date|string|number} dateTime - 日期时间对象、字符串或时间戳
 * @returns {string} 格式化后的日期字符串，格式为 xxxx/xx/xx xx:xx
 */
export function formatDateTimeShort(dateTime) {
  if (!dateTime) return '-'
  
  const date = new Date(dateTime)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '-'
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}/${month}/${day} ${hours}:${minutes}`
}

// 默认导出主要的格式化函数
export default formatDateTime
